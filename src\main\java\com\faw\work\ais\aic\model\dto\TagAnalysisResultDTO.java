package com.faw.work.ais.aic.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 标签分析结果DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "标签分析结果DTO")
public class TagAnalysisResultDTO {

    @Schema(description = "需求标签")
    private List<TagAnalysisDTO.DemandTag> demandTags;

    @Schema(description = "AI生成的需求标签")
    private List<TagAnalysisDTO.DemandTag> aiDemandTags;
}