package com.faw.work.ais.aic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MessageQueueMapper extends BaseMapper<MessageQueue> {
    /**
     * 按消息id选择
     *
     * @param messageId 消息id
     * @return {@link MessageQueue }
     */
    MessageQueue selectByMessageId(String messageId);

    /**
     * 根据请求ID查询消息队列列表
     *
     * @param requestId 请求ID
     * @return {@link List<MessageQueue>} 消息队列列表
     */
    List<MessageQueue> selectByMessageContent(String requestId);
}