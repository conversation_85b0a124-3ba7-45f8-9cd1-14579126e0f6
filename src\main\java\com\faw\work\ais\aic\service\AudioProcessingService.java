package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.controller.dto.TimeStampDTO;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 音频处理服务接口
 *
 * <AUTHOR>
 */
public interface AudioProcessingService {

    /**
     * 根据时间戳切分并合并音频
     *
     * @param audioUrl   音频文件URL
     * @param timestamps 时间戳字符串，格式：00:12:15-00:12:15,00:14:52-00:14:55
     * @return 合并后的音频文件字节数组
     * @throws IOException 处理异常
     */
    byte[] splitAndMergeAudio(String audioUrl, String timestamps) throws IOException;

    /**
     * 解析时间戳字符串为TimeStampDTO列表
     *
     * @param timestamps 时间戳字符串
     * @return TimeStampDTO列表
     */
    List<TimeStampDTO> parseTimestamps(String timestamps);

    /**
     * 预处理音频文件，为每个LlmRecord生成对应的音频片段文件
     *
     * @param audioUrl   音频文件URL
     * @param llmRecords LlmRecord列表，每个记录包含field3时间戳信息
     * @return 返回Map，key为llmRecordId，value为对应的音频文件路径
     * @throws IOException 处理异常
     */
    Map<Long, String> preprocessAudioFiles(String audioUrl, List<LlmRecord> llmRecords) throws IOException;

    /**
     * 根据文件路径读取音频文件字节数组
     *
     * @param filePath 音频文件路径
     * @return 音频文件字节数组
     * @throws IOException 读取异常
     */
    byte[] readAudioFile(String filePath) throws IOException;

    /**
     * 清理临时文件
     *
     * @param files 要清理的文件列表
     */
    void cleanupTempFiles(List<File> files);

    /**
     * 清理指定的音频文件
     *
     * @param filePath 要清理的文件路径
     */
    void cleanupAudioFile(String filePath);
}
