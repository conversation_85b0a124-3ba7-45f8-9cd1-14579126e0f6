package com.faw.work.ais.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 音频切分响应DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "音频切分响应DTO")
public class AudioSplitResponse {

    @Schema(description = "输出目录")
    private String outputDirectory;

    @Schema(description = "分片文件列表")
    private List<String> segmentFiles;

    @Schema(description = "总分片数")
    private Integer totalSegments;
}
