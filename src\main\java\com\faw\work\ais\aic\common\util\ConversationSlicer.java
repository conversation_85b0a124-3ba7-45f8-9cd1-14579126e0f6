package com.faw.work.ais.aic.common.util;

import cn.hutool.core.util.StrUtil;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 对话切片工具类。
 * <AUTHOR>
 */
public class ConversationSlicer {

    private static final Pattern LINE_PATTERN = Pattern.compile("^(\\d{2}:\\d{2}:\\d{2})\\s*([^:]+):\\s*(.*)$");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    /**
     * 解析单行对话。
     *
     * @param line 原始对话行。
     * @return 解析后的 ConversationLine 对象，如果无法解析则返回 null。
     */
    private static ConversationLine parseConversationLine(String line) {
        Matcher matcher = LINE_PATTERN.matcher(line);
        if (matcher.matches()) {
            try {
                LocalTime startTime = LocalTime.parse(matcher.group(1), TIME_FORMATTER);
                String speaker = matcher.group(2).trim();
                String content = matcher.group(3).trim();
                return new ConversationLine(startTime, speaker, content, line);
            } catch (DateTimeParseException e) {
                System.err.println("Error parsing time in line: " + line + " - " + e.getMessage());
                return null;
            }
        }
        return null;
    }

    /**
     * 为所有对话行添加结束时间戳。
     * 结束时间通常是下一句话的开始时间，最后一句的结束时间根据其开始时间加上一个预设值。
     *
     * @param parsedLines 已经解析过的对话行列表。
     * @return 填充了结束时间的对话行列表。
     */
    private static List<ConversationLine> addEndTime(List<ConversationLine> parsedLines) {
        if (parsedLines == null || parsedLines.isEmpty()) {
            return new ArrayList<>();
        }

        for (int i = 0; i < parsedLines.size(); i++) {
            ConversationLine currentLine = parsedLines.get(i);
            if (i < parsedLines.size() - 1) {
                currentLine.endTime = parsedLines.get(i + 1).startTime;
            } else {
                currentLine.endTime = currentLine.startTime.plusSeconds(1);
            }
        }
        return parsedLines;
    }

    /**
     * 连接指定范围内的对话行，并提取客户发言时间戳。
     *
     * @param linesWithEndTime 包含结束时间的对话行列表。
     * @param startIndex       分片的起始索引（包含）。
     * @param endIndex         分片的结束索引（包含）。
     * @param customerSpeaker  客户的说话人标识。
     * @return MapEntry，key为分片内容，value为客户发言时间戳字符串。
     */
    private static Map.Entry<String, String> joinLinesAndExtractCustomerTimestamps(
            List<ConversationLine> linesWithEndTime, int startIndex, int endIndex, String customerSpeaker) {

        StringBuilder sliceContentBuilder = new StringBuilder();
        List<String> customerTimestamps = new ArrayList<>();

        for (int i = startIndex; i <= endIndex; i++) {
            if (i < 0 || i >= linesWithEndTime.size()) {
                continue;
            }
            ConversationLine line = linesWithEndTime.get(i);
            sliceContentBuilder.append(line.originalLine).append("\n");

            if (line.speaker.equals(customerSpeaker)) {
                String start = line.startTime.format(TIME_FORMATTER);
                String end = line.endTime.format(TIME_FORMATTER);
                customerTimestamps.add(start + "-" + end);
            }
        }

        String sliceKey = sliceContentBuilder.toString().trim();
        String sliceValue = customerTimestamps.isEmpty() ? "" : String.join(",", customerTimestamps);

        return new TreeMap.SimpleEntry<>(sliceKey, sliceValue);
    }

    /**
     * 对话切片方法，输出的key为分片的内容，value为对应key中是客户发言的每一句话的开始和结束时间戳。
     * 优化了稀疏客户发言的切片逻辑。
     *
     * @param fullConversation   完整的对话内容字符串。
     * @param customerSpeaker    客户的说话人标识。
     * @param minCustomerSentences 最小客户发言句数。
     * @param overlapLines       分片重叠的行数。
     * @return Map，key为分片内容，value为客户发言时间戳字符串 (HH:MM:SS-HH:MM:SS_HH:MM:SS-HH:MM:SS)。
     */
    public static Map<String, String> sliceConversationV2(String fullConversation,
                                                          String customerSpeaker,
                                                          int minCustomerSentences,
                                                          int overlapLines) {
        Map<String, String> conversationSlices = new TreeMap<>();

        if (StrUtil.isBlank(fullConversation)) {
            return conversationSlices;
        }

        String[] rawLines = fullConversation.split("\\r?\\n");
        List<ConversationLine> allParsedLines = new ArrayList<>();
        for (String rawLine : rawLines) {
            if (StrUtil.isNotBlank(rawLine)) {
                ConversationLine parsedLine = parseConversationLine(rawLine);
                if (parsedLine != null) {
                    allParsedLines.add(parsedLine);
                }
            }
        }

        if (allParsedLines.isEmpty()) {
            return conversationSlices;
        }

        List<ConversationLine> linesWithEndTime = addEndTime(allParsedLines);

        int currentProcessingStartIndex = 0; // 当前处理的窗口的起始索引
        List<Integer> customerSentenceIndicesInWindow = new ArrayList<>(); // 记录当前窗口内客户发言的索引

        for (int i = 0; i < linesWithEndTime.size(); i++) {
            ConversationLine currentLine = linesWithEndTime.get(i);

            // 将当前客户发言的索引加入列表
            if (currentLine.speaker.equals(customerSpeaker)) {
                customerSentenceIndicesInWindow.add(i);
            }

            // 移除所有在当前处理窗口起始索引之前的客户发言索引
            // 确保 customerSentenceIndicesInWindow 只包含从 currentProcessingStartIndex 到 i 的客户发言
            Iterator<Integer> iterator = customerSentenceIndicesInWindow.iterator();
            while (iterator.hasNext()) {
                if (iterator.next() < currentProcessingStartIndex) {
                    iterator.remove();
                } else {
                    // 因为列表是按索引升序添加的，一旦遇到不小于 currentProcessingStartIndex 的，
                    // 后面的也肯定不小于，可以直接跳出。
                    break;
                }
            }

            // 如果当前窗口内客户发言数量达到最小要求
            if (customerSentenceIndicesInWindow.size() >= minCustomerSentences) {
                // 确定切片的结束索引为当前窗口中最后一个客户发言的索引
                int lastCustomerSentenceIndexInSlice = customerSentenceIndicesInWindow.get(customerSentenceIndicesInWindow.size() - 1);

                // 生成并添加切片。切片范围是 [currentProcessingStartIndex, lastCustomerSentenceIndexInSlice]
                Map.Entry<String, String> sliceEntry =
                        joinLinesAndExtractCustomerTimestamps(linesWithEndTime, currentProcessingStartIndex, lastCustomerSentenceIndexInSlice, customerSpeaker);

                // 只有当分片内容不为空时才添加，避免添加空内容切片
                if (StrUtil.isNotBlank(sliceEntry.getKey())) {
                    conversationSlices.put(sliceEntry.getKey(), sliceEntry.getValue());
                }

                // 更新下一个分片的起始位置
                if (overlapLines > 0) {
                    // 有重叠：新的起始位置从当前切片的最后一个客户发言索引向前回溯 overlapLines 行。
                    // 确保不会回溯到负数索引。
                    currentProcessingStartIndex = Math.max(0, lastCustomerSentenceIndexInSlice - overlapLines + 1);
                } else {
                    // 无重叠：新的起始位置是当前切片中最后一个客户发言的下一行。
                    currentProcessingStartIndex = lastCustomerSentenceIndexInSlice + 1;
                }

                // 在更新 currentProcessingStartIndex 后，再次清理 customerSentenceIndicesInWindow 列表。
                // 移除所有小于新的 currentProcessingStartIndex 的索引，为下一个切片窗口做准备。
                iterator = customerSentenceIndicesInWindow.iterator(); // 获取新的迭代器
                while (iterator.hasNext()) {
                    if (iterator.next() < currentProcessingStartIndex) {
                        iterator.remove();
                    } else {
                        break;
                    }
                }
            }
        }

        // 处理剩余的文本：如果循环结束后，currentProcessingStartIndex 仍小于总行数，
        // 意味着对话末尾部分可能未被切片。这部分内容应该被包含，即使客户发言不足 minCustomerSentences。
        if (currentProcessingStartIndex < linesWithEndTime.size()) {
            Map.Entry<String, String> sliceEntry =
                    joinLinesAndExtractCustomerTimestamps(linesWithEndTime, currentProcessingStartIndex, linesWithEndTime.size() - 1, customerSpeaker);
            // 只有当最后切片包含内容时才添加。
            if (StrUtil.isNotBlank(sliceEntry.getKey())) {
                conversationSlices.put(sliceEntry.getKey(), sliceEntry.getValue());
            }
        }

        return conversationSlices;
    }

    /**
     * 表示一个对话行。
     * <AUTHOR>
     */
    public static class ConversationLine {
        LocalTime startTime;
        LocalTime endTime;
        String speaker;
        String content;
        String originalLine;

        public ConversationLine(LocalTime startTime, String speaker, String content, String originalLine) {
            this.startTime = startTime;
            this.speaker = speaker;
            this.content = content;
            this.originalLine = originalLine;
        }
    }
}
