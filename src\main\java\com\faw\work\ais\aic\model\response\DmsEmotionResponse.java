package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.dto.EmotionResponseSummaryDTO;
import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DmsEmotionResponse {

    /**
     * 请求id
     */
    @Schema(description = "请求ID")
    private String requestId;

    /**
     * 整个接待情绪总结
     */
    @Schema(description = "整个接待情绪总结")
    private String emotionSummary;

    /**
     * 情绪数据-情绪识别模型
     */
    @Schema(description = "语音语气上线后废弃")
    private EmotionResponseSummaryDTO emotionData;

    /**
     * 标签数据-产品需求模型
     */
    private List<TagAnalysisDTO> tagData;

    /**
     * 话题总结数据-话题总结模型
     */
    private TopicSummaryDTO topicData;
}
