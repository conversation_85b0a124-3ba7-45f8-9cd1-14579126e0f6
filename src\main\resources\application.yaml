server:
  port: 8080

spring:
  profiles:
    active: uat
  application:
    name: msa-aio
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  cloud:
    nacos:
      config:
        server-addr: localhost:8848
      discovery:
        server-addr: localhost:8848

  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB
  datasource:
    ais:
      driver-class-name: com.mysql.jdbc.Driver
      pool-name: product-operation-pool
      jdbc-url: ***************************************************************************************************************************************
      username: root
      password: root
#    ais:
#      driver-class-name: com.mysql.jdbc.Driver
#      pool-name: product-operation-pool
#      jdbc-url: ********************************************************************************************************************************************
#      username: 10652c95-22e9-4267-b261-fd6c14a995f1
#      password: d3p7zau40fIzPl9q
      # HikariCP连接池配置
      hikari:
        maximum-pool-size: 50          # 最大连接数，支持多线程并发
        minimum-idle: 5                # 最小空闲连接数
        connection-timeout: 30000      # 连接超时时间（毫秒）
        idle-timeout: 600000           # 空闲连接超时时间（毫秒）
        max-lifetime: 1800000          # 连接最大生命周期（毫秒）
        leak-detection-threshold: 60000 # 连接泄漏检测阈值（毫秒）

  ai:
    dashscope:
      api-host: https://dashscope.aliyuncs.com
      api-key: sk-84d50a726d4f4253b04dcd47f35a4b08
      model: deepseek-r1
      rerank:
        api-key: ***********************************
        options:
          model: gte-rerank-v2
  rabbitmq:
    listener:
      simple:
        queues: bnzx_queue  # 必须与 RabbitMQ 中的队列名匹配
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 0
  fileStorage:
    bucketName: yggztyx-cs
    accessKey: D6LVUXBG7LY1EHGB2RHF
    secretKey: LlmYIoV0Un1wgCJ5IVvONd2eXVpjXjHt5BSDTjgW
    endPoint: obs.cn-northeast-204.faw.com.cn
    filePath: /aio/uat/
    storageType: OBS
    bucketName.null: yggztyx-cs
    accessKey.null: D6LVUXBG7LY1EHGB2RHF
    secretKey.null: LlmYIoV0Un1wgCJ5IVvONd2eXVpjXjHt5BSDTjgW
    endPoint.null: obs.cn-northeast-204.faw.com.cn
    filePath.null: /aio/uat/
    storageType.null: OBS
redis:
  host: 127.0.0.1
  port: 6379
  password:
  database: 0
ugcconfig:
  hostUrl: https://uat-api.faw.cn:30443
  appSecret: 21c40f91d0e64cbf8a23c1f683f324d5
  appKey: 9601007f36ce46dda67701d99e623446
ugcprodconfig:
  prodHostUrl: https://prod-api.faw.cn
  prodAppSecret: 2d71d62d7a3c48e8b4c3bec0967fec5c
  prodAppKey: 9d56bb2d1095462cb4a2b772f4d03a04
file:
  upload:
    secretId: AKIDhGqZyXJEIMQdjy7HrSGGlGrwPlBavAWA
    secretKey: jQ0VxEZIvHbvBMcpw0xqqmty6970rqpo
    bucketName: ais-**********
    regionName: ap-beijing
    filePath: /prod/ais/
    allowPicTypes: #文件上传允许的类型
      - image/jpeg
      - image/png
      - image/bmp
    allowPPTTypes: #文件上传允许的类型
      - application/vnd.openxmlformats-officedocument.presentationml.presentation
    contentType: application/octet-stream
mq:
  exchange-name: ais_exchange
  queue-name: ais_queue
  routing-key: ais_route
  aisErrorQueue:
    queue-name: queue_error
    routing-key: route_error
  bnzxQueue:
    queue-name: bnzx_queue
    routing-key: bnzx_route
  openCleanData: false
http:
  log:
    enable: true
feign:
  python:
    hostUrl: https://rw-uat.faw.cn/ais
    # hostUrl: https://rw-sit.faw.cn
  openApi:
    hostUrl: https://uat-api.faw.cn:30443
    BNZX: /JT/SA/SA-0201/TDS/tds/gbAiCheckResult
  client:
    config:
      default:
        connectTimeoutMillis: 1800000
        readTimeoutMillis: 1800000
aod:
  scDataUrl: "http://*************:8107/api/events/report?project=production&token=fe6be2f907c4e32a3a5fef17bd1d95414615ccc273993fae63c0153cf1d54035"
  pvDayTarget: 250000
  pvMonthTarget: 900000
  uvDayTarget: ********
  uvMonthTarget: *********

ding:
  dingKey: dingjitkz7detaf87upo
  dingShift: **********
  dingSecret: DGKWOamgjnLs3diTdhq_vMb9Pul8t8eSFBffbhwZ50EDFiAMxWFGfSFYs0G-HwP0
  dingOwner: 301912434926155694
  robot:
    access_token: 731edaa393026e01c19df9bf021a0de0fe3de5072a75d9808fd4d5b03daf5ffe
    Url: https://oapi.dingtalk.com/robot/send
    robotSecret: SEC2b8ca9bc74e959f0656d04e5e5a91cf8a2a8384096fd32c16e03ffc0dabd1492
    keyWordOne: AI审核监控报警
    keyWordTwo: 大模型返回数据格式错
  userId:
    ruleError: 10s-hyf7jwzmrh
    otherError: 1v6_sr4jooj8wg
whitelist:
  systemIds:
    - "YSZC-ESC"
    - "YKYY"
    - "RWYYZX"
    - "BNZX"
    - "SWZC"
    - "TDSCLM01"
    - "MSASVC"
    - "HIS"
  # 文件必填项校白名单
  fileUndoChecks:
    - "RWYYZX"
    - "HIS"
  # 审核单id也是批次id字段必填项校验白名单-白名单内不需要校验
  batchIdChecks:
    - "RWYYZX"
    - "BNZX"
    - "HIS"
# 数字员工看板配置
numberEmployee:
  # 处理人
  #  handler: 智能审核
  handler: 陈红
  dealerCode : 11
rw:
  url: https://rw-uat.faw.cn/rwgateway

bpm:
  behaviorFlowTemplate:
    tenantLimitId: r2sc8ufe
    appsource: SA-0105
    category: SA-0105.new_behavior_application
    sysCode: SA-0105
    procCode: SA-0105.flow
    templateName: 用户洞察-新建行为申请审批流
  labelApplicationFlowTemplate:
    tenantLimitId: r2sc8ufe
    appsource: SA-0105
    category: SA-0105.new_label_application
    sysCode: SA-0105
    procCode: SA-0105.flow
    templateName: 用户洞察-新建标签申请审批流
# 用户洞察角色工作台任务流程配置
user-insight-rw-task-flow-config:
  appKey: 1538dcd52e134cb2a7d90b8e8e78595f
  appSecret: 74ad89e0c2e849299c89186bc12246a3
  # 标签行为管理
  labelAndBehavior:
    bizUnitCode: ******************
    startEventCode: ET-152374942690770944
    closeEventCode: EC-152374972285779968
    designUserAccount: wangxin1
  # 用户组管理
  userGroup:
    bizUnitCode: ******************
    startEventCode: ET-152375037024862208
    closeEventCode: EC-152375073704050688
  # 新建场景
  createNewScene:
    bizUnitCode: ******************
    startEventCode: ET-152374942690770944
    closeEventCode: EC-152374972285779968
  # 场景监控与优化
  sceneMonitoringOptimization:
    bizUnitCode: ******************
    startEventCode: ET-152374942690770944
    closeEventCode: EC-152374972285779968
#用户洞察角色工作台任务跳转参数
user-insight-task-jump-config:
  host: https://uat-iwork.faw.cn/
  #  新建标签跳转参数
  labelCreateParam: { target: 'LabelInfo'}
  #  设计标签跳转参数
  labelDesignParam: { target: 'LabelDesign'}
  #  新建行为跳转参数
  behaviorCreateParam: { target: 'BehaviorInfo' }
  #  设计行为跳转参数
  behaviorDesignParam: { target: 'BehaviorDesign' }
  #  设计行为跳转参数
  userGroupParam: { target: 'Crowd' }
tencent:
  ocr:
    secretId: AKIDa4LTZXP5XkjJ59A4lKuQxPyRnBYr6GNN
    secretKey: zlLk9QrHmKsR6jJxiVEt3BHTlwa5c4iE
    callBackUrl: https://uat-api.faw.cn:30443/JT/SA/SA-0201/TDS/tds/gbAiCheckResult

async:
  executor:
    thread:
      #配置核心线程数
      core_pool_size: 3
      #配置最大线程数日
      max_pool_size: 18
      #配置队列大小
      queue_capacity: 99988
      #设置线程空闲等待时间秒
      alive_seconds: 20
      name:
        #配置线程池中的线程的名称前缀
        prefix: async-thread-
      keep_alive_seconds: 20
      rejectedExecutionHandler: CallerRunsPolicy
    numberEmployeeThread:
      #配置核心线程数
      core_pool_size: 30
      #配置最大线程数日
      max_pool_size: 200
      #配置队列大小
      queue_capacity: 100
      #设置线程空闲等待时间秒
      alive_seconds: 60

aiSave:
  saveTime: 2
  saveCost: 5

milvus:
  host: http://c-649f5b46b97336c5.milvus.aliyuncs.com
  port: 19530
  username: FawVoice
  password: Admin199401@
  dbName: aio
#milvus:
#  host: http://c-ceddcf5be4375f2c.milvus.aliyuncs.com
#  port: 19530
#  username: FawVoice
#  password: Admin199401@
#  dbName: aio
ali:
  embedding:
    api-key: sk-e47998393fd84c52b4171714c8470144
  omini:
    api-key: sk-e47998393fd84c52b4171714c8470144
  run: false

dashscope:
  dms:
    workspace: llm-o3okbs9bepvf8nhl
    emotion:
      apiKey: sk-505290d6e9464ae6a5283b343bb39847
      analysis-appId: b269671052a8438e9678b9ec58110ff3
      tag-appId: cddede0d78664fbf8f8ee9b3e338d359
      topic-appId: 5e330236fb204f82a868eb11da76f36c
      topic-analysis-v2-appId: 8fa93e4857ae44c8a822130e1e42ef7e
      tag-demand-analysis-v2-appId: 2ee659428b0e4a7f84742be089653de7
  lingxiaoxi:
    workspace: llm-wipob7d3ruuuhsx3
    chat:
      apiKey: sk-84d50a726d4f4253b04dcd47f35a4b08
      reQuery-appId: 73f189fadb26431d8029832ed075e9d6
  yanggou:
    workspace: llm-1i87fdassiufiviz
    apiKey: sk-8118b17d391e4ad689e8d719a82d12a2
    kehuhuaxiang:
      appId: bd4e08334eb04ee89a85a00f51ab7d37
    highQualitySpeech: e877ecf60c8b45e99e72941a070b0d4b
    highQualityPrompt: 89dcbfc254cf4bda96088748c1788078
    highQualityKnowledge: d092ef931d31402e8eeaac3c66172fd3
    knowledge:
      env: prod
      robot-id: 53f9aa42168bae9efa252834d2b31d05
      topK: 1
      similarityThreshold: 0.6
rag:
  document:
    uploadSize: 100
aic:
  config:
    environment: uat
    ucg:
      app-key: 9601007f36ce46dda67701d99e623446
      app-secret: 21c40f91d0e64cbf8a23c1f683f324d5
      host: https://uat-api.faw.cn:30443
      dms-knowledge-center-host: https://miw-coc-uat.faw.cn
#aic:
#  config:
#    environment: prod
#    ucg:
#      app-key: 9d56bb2d1095462cb4a2b772f4d03a04
#      app-secret: 2d71d62d7a3c48e8b4c3bec0967fec5c
#      host: https://prod-api.faw.cn
#      dms-knowledge-center-host: https://miw-coc.faw.cn
