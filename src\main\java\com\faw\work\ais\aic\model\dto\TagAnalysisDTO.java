package com.faw.work.ais.aic.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 标签分析返回结果DTO
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TagAnalysisDTO {
    /**
     * 输入
     */
    private String input;
    /**
     * 分片ID
     */
    private Long llmRecordId;

    /**
     * 开始时间戳
     */
    private String start;


    /**
     * 结束时间戳
     */
    private String end;

    /**
     * 问题总结
     */

    private List<TopicSummary> customerQuestionSummaries;

    /**
     * 话题总结
     */

    private List<TopicSummary> topicSummaries;

    /**
     * 客户情绪分析（语义维度）
     */
    @Schema(description = "客户情绪分析（语义维度）")
    private EmotionAnalysis semanticEmotion;

    /**
     * 客户情绪分析（语气维度）
     */
    @Schema(description = "客户情绪分析（语气维度）")
    private EmotionAnalysis toneEmotion;

    /**
     * 客户情绪分析（行为维度）
     */
    @Schema(description = "客户情绪分析（行为维度）")
    private EmotionAnalysis behaviorEmotion;

    /**
     * 需求标签
     */
    @Schema(description = "需求标签")
    private List<DemandTag> demandTags;

    /**
     * AI生成的需求标签
     */
    @Schema(description = "AI生成的需求标签")
    private List<DemandTag> aiDemandTags;



    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Schema(description = "话题总结")
    public static class TopicSummary {
        @JsonProperty("question")
        @Schema(description = "问题")
        private String question;

        @JsonProperty("answer")
        @Schema(description = "答案")
        private String answer;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Schema(description = "需求标签")
    public static class DemandTag {
        @JsonProperty("level1")
        private String level1;

        @JsonProperty("level2")
        private String level2;

        @JsonProperty("level3")
        private String level3;

        @JsonProperty("tagEmotion")
        private String tagEmotion;

        @JsonProperty("tagEmotionReason")
        private String tagEmotionReason;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Schema(description = "情绪分析")
    public static class EmotionAnalysis {
        /**
         * 情绪分类
         */
        @JsonProperty("emotion")
        @Schema(description = "情绪分类")
        private String emotion;

        /**
         * 情绪得分
         */
        @JsonProperty("score")
        @Schema(description = "情绪得分")
        private String score;

        /**
         * 情绪原因
         */
        @JsonProperty("reason")
        @Schema(description = "情绪原因")
        private String reason;
    }
} 