package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.model.domain.MessageQueue;

import java.util.List;

/**
 * 消息队列服务
 *
 * <AUTHOR>
 */
public interface MessageQueueService extends IService<MessageQueue> {

    /**
     * 根据消息ID获取消息队列信息
     *
     * @param messageId 消息ID
     * @return 消息队列信息
     */
    MessageQueue getByMessageId(String messageId);


    /**
     * 更新状态
     *
     * @param messageId 消息id
     * @param code      状态
     * @param remark    备注
     */
    void updateStatus(String messageId, MessageStatus code,String remark);

    /**
     * 通过消息内容获取
     *
     * @param requestId 请求id
     * @return {@link List }<{@link MessageQueue }>
     */
    List<MessageQueue> getByMessageContent(String requestId);
}