package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大模型业务类型枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LLMBizTypeEnum {
    /**
     * 情绪识别模型
     */
    DMS_EMOTION("dms_emotion", "情绪识别模型"),

    DMS_EMOTION_V2("dms_emotion_v2", "情绪识别模型V2"),
    /**
     * 情绪识别模型
     */
    DMS_PRODUCT("dms_product", "产品需求模型");

    private final String code;
    private final String desc;

}