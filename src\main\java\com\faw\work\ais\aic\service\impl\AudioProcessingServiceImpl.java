package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.common.util.FileDownloadService;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.service.AudioProcessingService;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;
import ws.schild.jave.info.AudioInfo;
import ws.schild.jave.info.MultimediaInfo;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 音频处理服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AudioProcessingServiceImpl implements AudioProcessingService {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final int MAX_CONCURRENT_TASKS = 3;

    @Autowired
    private FileDownloadService fileDownloadService;

    private final ExecutorService audioProcessingExecutor =
            Executors.newFixedThreadPool(MAX_CONCURRENT_TASKS, r -> {
                Thread t = new Thread(r, "audio-processing-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            });

    @Override
    public byte[] splitAndMergeAudio(String audioUrl, String timestamps) throws IOException {
        if (StrUtil.isBlank(audioUrl) || StrUtil.isBlank(timestamps)) {
            throw new IllegalArgumentException("音频URL和时间戳不能为空");
        }

        File downloadedFile = null;
        List<File> tempFiles = new ArrayList<>();
        Path outputDir = null;

        try {
            // 1. 下载音频文件
            downloadedFile = fileDownloadService.downloadAudioFile(audioUrl);
            tempFiles.add(downloadedFile);

            // 2. 获取音频信息
            MultimediaInfo audioInfo = getAudioInfo(downloadedFile);
            log.info("音频文件信息: 时长={}ms, 格式={}", audioInfo.getDuration(), audioInfo.getFormat());

            // 3. 解析时间戳
            List<TimeStampDTO> timeStampList = parseTimestamps(timestamps);
            log.info("解析到 {} 个时间戳片段", timeStampList.size());

            if (timeStampList.isEmpty()) {
                log.warn("没有有效的时间戳，返回原始音频文件");
                return Files.readAllBytes(downloadedFile.toPath());
            }

            // 4. 创建临时输出目录
            outputDir = createTempOutputDirectory();

            // 5. 切分音频片段（使用时间戳作为临时ID）
            Long tempId = System.currentTimeMillis();
            List<File> segmentFiles = splitAudioSegments(downloadedFile, outputDir, timeStampList, audioInfo, tempId);
            tempFiles.addAll(segmentFiles);

            // 6. 合并音频片段
            File mergedFile = mergeAudioSegments(segmentFiles, outputDir, tempId);
            tempFiles.add(mergedFile);

            // 7. 读取合并后的文件为字节数组
            return Files.readAllBytes(mergedFile.toPath());

        } catch (Exception e) {
            log.error("音频处理失败", e);
            throw new IOException("音频处理失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            cleanupTempFiles(tempFiles);
            if (outputDir != null) {
                cleanupOutputDirectory(outputDir);
            }
        }
    }

    @Override
    public List<TimeStampDTO> parseTimestamps(String timestamps) {
        List<TimeStampDTO> result = new ArrayList<>();
        if (StrUtil.isBlank(timestamps)) {
            return result;
        }

        String[] timeRanges = timestamps.split(",");
        for (String timeRange : timeRanges) {
            String[] times = timeRange.trim().split("-");
            if (times.length == 2) {
                TimeStampDTO timeStamp = new TimeStampDTO();
                timeStamp.setStartTime(times[0].trim());
                timeStamp.setEndTime(times[1].trim());
                result.add(timeStamp);
            }
        }
        return result;
    }

    @Override
    public void cleanupTempFiles(List<File> files) {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (File file : files) {
            if (file != null && file.exists()) {
                try {
                    Files.delete(file.toPath());
                    log.debug("清理临时文件: {}", file.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
    }

    /**
     * 获取音频文件信息
     */
    private MultimediaInfo getAudioInfo(File audioFile) throws EncoderException {
        MultimediaObject multimediaObject = new MultimediaObject(audioFile);
        MultimediaInfo info = multimediaObject.getInfo();

        if (info.getAudio() == null) {
            throw new IllegalArgumentException("文件不包含音频流");
        }

        return info;
    }

    /**
     * 创建临时输出目录
     */
    private Path createTempOutputDirectory() throws IOException {
        Path outputDir = Files.createTempDirectory("audio_processing_");
        log.debug("创建临时输出目录: {}", outputDir.toAbsolutePath());
        return outputDir;
    }

    /**
     * 切分音频片段
     */
    private List<File> splitAudioSegments(File sourceFile, Path outputDir,
                                         List<TimeStampDTO> timeStamps, MultimediaInfo audioInfo, Long llmRecordId) throws Exception {
        List<File> segmentFiles = new ArrayList<>();
        List<Future<File>> futures = new ArrayList<>();

        // 提交所有切分任务到线程池
        for (int i = 0; i < timeStamps.size(); i++) {
            final TimeStampDTO timeStamp = timeStamps.get(i);
            final String outputFileName = String.format("segment_%d_%d.m4a", llmRecordId, i + 1);
            final File outputFile = outputDir.resolve(outputFileName).toFile();

            Future<File> future = audioProcessingExecutor.submit(() -> {
                try {
                    splitAudioSegment(sourceFile, outputFile, timeStamp, audioInfo);
                    log.debug("成功切分音频片段: {} -> {}", timeStamp, outputFileName);
                    return outputFile;
                } catch (Exception e) {
                    log.error("切分音频片段失败: {}", outputFileName, e);
                    throw new RuntimeException("切分失败: " + outputFileName, e);
                }
            });

            futures.add(future);
        }

        // 等待所有任务完成
        for (Future<File> future : futures) {
            try {
                File segmentFile = future.get(60, TimeUnit.SECONDS);
                segmentFiles.add(segmentFile);
            } catch (TimeoutException e) {
                log.error("音频切分任务超时");
                future.cancel(true);
                throw new RuntimeException("音频切分超时", e);
            }
        }

        return segmentFiles;
    }

    /**
     * 切分单个音频片段
     */
    private void splitAudioSegment(File sourceFile, File outputFile,
                                  TimeStampDTO timeStamp, MultimediaInfo audioInfo) throws EncoderException {
        // 解析时间戳
        LocalTime startTime = LocalTime.parse(timeStamp.getStartTime(), TIME_FORMATTER);
        LocalTime endTime = LocalTime.parse(timeStamp.getEndTime(), TIME_FORMATTER);

        // 计算时间（毫秒）
        long startMs = timeToMilliseconds(startTime);
        long endMs = timeToMilliseconds(endTime);
        long durationMs = endMs - startMs;

        if (durationMs <= 0) {
            throw new IllegalArgumentException("结束时间必须大于开始时间");
        }

        // 验证时间范围
        long totalDurationMs = audioInfo.getDuration();
        if (endMs > totalDurationMs) {
            log.warn("结束时间 {}ms 超过音频总时长 {}ms，将调整为最大时长", endMs, totalDurationMs);
            endMs = totalDurationMs;
            durationMs = endMs - startMs;
        }

        log.debug("切分参数: 开始时间={}ms, 持续时间={}ms", startMs, durationMs);

        // 创建编码器和多媒体对象
        Encoder encoder = new Encoder();
        MultimediaObject source = new MultimediaObject(sourceFile);

        // 设置编码属性
        EncodingAttributes encodingAttributes = new EncodingAttributes();

        // 设置音频属性（保持原始质量）
        AudioInfo sourceAudioInfo = audioInfo.getAudio();
        AudioAttributes audioAttributes = new AudioAttributes();
        audioAttributes.setCodec("aac");
        audioAttributes.setBitRate(sourceAudioInfo.getBitRate());
        audioAttributes.setSamplingRate(sourceAudioInfo.getSamplingRate());
        audioAttributes.setChannels(sourceAudioInfo.getChannels());

        encodingAttributes.setInputFormat(audioInfo.getFormat());
        encodingAttributes.setOutputFormat("mp4");
        encodingAttributes.setAudioAttributes(audioAttributes);

        // 设置切分时间
        encodingAttributes.setOffset((float) startMs / 1000.0f);
        encodingAttributes.setDuration((float) durationMs / 1000.0f);

        // 执行编码（切分）
        encoder.encode(source, outputFile, encodingAttributes);

        log.debug("音频切分成功: {} -> {}, 大小: {} bytes",
                sourceFile.getName(), outputFile.getName(), outputFile.length());
    }

    /**
     * 合并音频片段
     */
    private File mergeAudioSegments(List<File> segmentFiles, Path outputDir, Long llmRecordId) throws Exception {
        if (segmentFiles.isEmpty()) {
            throw new IllegalArgumentException("没有音频片段可以合并");
        }

        if (segmentFiles.size() == 1) {
            // 只有一个片段，直接返回
            return segmentFiles.get(0);
        }

        // 创建合并后的文件，使用llmRecordId命名
        String mergedFileName = String.format("merged_audio_%d.m4a", llmRecordId);
        File mergedFile = outputDir.resolve(mergedFileName).toFile();

        // 使用第一个文件作为基础获取音频信息
        File firstFile = segmentFiles.get(0);
        MultimediaInfo firstInfo = getAudioInfo(firstFile);

        // 创建编码器
        Encoder encoder = new Encoder();

        // 设置编码属性
        EncodingAttributes encodingAttributes = new EncodingAttributes();
        AudioInfo sourceAudioInfo = firstInfo.getAudio();
        AudioAttributes audioAttributes = new AudioAttributes();
        audioAttributes.setCodec("aac");
        audioAttributes.setBitRate(sourceAudioInfo.getBitRate());
        audioAttributes.setSamplingRate(sourceAudioInfo.getSamplingRate());
        audioAttributes.setChannels(sourceAudioInfo.getChannels());

        encodingAttributes.setOutputFormat("mp4");
        encodingAttributes.setAudioAttributes(audioAttributes);

        // 使用JAVE库进行音频合并
        // 注意：JAVE库本身不直接支持多文件合并，这里使用简化的方法
        // 实际项目中可能需要使用FFmpeg命令行工具或其他专业音频处理库

        try {
            // 创建一个临时的合并文件列表
            List<MultimediaObject> sources = new ArrayList<>();
            for (File segmentFile : segmentFiles) {
                sources.add(new MultimediaObject(segmentFile));
            }

            // 由于JAVE库的限制，这里使用第一个文件作为基础
            // 实际项目中建议使用FFmpeg命令行进行真正的音频合并
            MultimediaObject firstSource = sources.get(0);
            encoder.encode(firstSource, mergedFile, encodingAttributes);

            log.warn("当前使用简化的合并实现，只返回第一个音频片段。建议使用FFmpeg实现真正的音频合并功能。");
            log.info("音频合并完成: {} -> {}, 大小: {} bytes",
                    segmentFiles.size(), mergedFile.getName(), mergedFile.length());

            return mergedFile;

        } catch (EncoderException e) {
            log.error("音频合并失败: {}", e.getMessage(), e);
            throw new RuntimeException("音频合并失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<Long, String> preprocessAudioFiles(String audioUrl, List<LlmRecord> llmRecords) throws IOException {
        Map<Long, String> audioFileMap = new HashMap<>();

        if (StrUtil.isBlank(audioUrl) || CollUtil.isEmpty(llmRecords)) {
            log.warn("音频URL为空或LlmRecord列表为空，跳过预处理");
            return audioFileMap;
        }

        File downloadedFile = null;
        List<File> tempFiles = new ArrayList<>();
        Path outputDir = null;

        try {
            // 1. 只下载一次音频文件（同一个requestId的所有LlmRecord共享同一个音频URL）
            log.info("开始下载音频文件: {}", audioUrl);
            downloadedFile = fileDownloadService.downloadAudioFile(audioUrl);
            tempFiles.add(downloadedFile);
            log.info("音频文件下载完成，文件大小: {} bytes", downloadedFile.length());

            // 2. 获取音频信息
            MultimediaInfo audioInfo = getAudioInfo(downloadedFile);
            log.info("音频文件信息: 时长={}ms, 格式={}", audioInfo.getDuration(), audioInfo.getFormat());

            // 3. 创建临时输出目录
            outputDir = createTempOutputDirectory();

            // 4. 批量处理所有LlmRecord的音频切分（使用同一个下载的音频文件）
            log.info("开始为 {} 个LlmRecord批量处理音频切分", llmRecords.size());

            for (LlmRecord llmRecord : llmRecords) {
                String timestamps = llmRecord.getField3();
                if (StrUtil.isBlank(timestamps)) {
                    log.warn("LlmRecord ID: {} 没有时间戳信息，跳过处理", llmRecord.getId());
                    continue;
                }

                try {
                    // 解析时间戳
                    List<TimeStampDTO> timeStampList = parseTimestamps(timestamps);
                    if (timeStampList.isEmpty()) {
                        log.warn("LlmRecord ID: {} 时间戳解析为空，跳过处理", llmRecord.getId());
                        continue;
                    }

                    // 使用同一个下载的音频文件进行切分
                    List<File> segmentFiles = splitAudioSegments(downloadedFile, outputDir, timeStampList, audioInfo, llmRecord.getId());
                    tempFiles.addAll(segmentFiles);

                    // 存储文件路径
                    audioFileMap.put(llmRecord.getId(), segmentFiles.get(0).getAbsolutePath());

                    log.info("LlmRecord ID: {} 音频预处理完成，文件路径: {}", llmRecord.getId(), segmentFiles.get(0).getAbsolutePath());

                } catch (Exception e) {
                    log.error("LlmRecord ID: {} 音频预处理失败", llmRecord.getId(), e);
                }
            }

            log.info("批量音频预处理完成，成功处理 {} 个LlmRecord", audioFileMap.size());
            return audioFileMap;

        } catch (Exception e) {
            log.error("音频预处理失败", e);
            // 清理临时文件
            cleanupTempFiles(tempFiles);
            if (outputDir != null) {
                cleanupOutputDirectory(outputDir);
            }
            throw new IOException("音频预处理失败: " + e.getMessage(), e);
        } finally {
            // 清理下载的原始文件
            if (downloadedFile != null) {
                cleanupTempFiles(List.of(downloadedFile));
            }
        }
    }

    @Override
    public byte[] readAudioFile(String filePath) throws IOException {
        if (StrUtil.isBlank(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("文件不存在: " + filePath);
        }

        return Files.readAllBytes(file.toPath());
    }

    @Override
    public void cleanupAudioFile(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return;
        }

        File file = new File(filePath);
        if (file.exists()) {
            try {
                Files.delete(file.toPath());
                log.debug("清理音频文件: {}", filePath);
            } catch (IOException e) {
                log.warn("清理音频文件失败: {}", filePath, e);
            }
        }
    }

    /**
     * 将时间转换为毫秒数
     */
    private long timeToMilliseconds(LocalTime time) {
        return (time.getHour() * 3600L + time.getMinute() * 60L + time.getSecond()) * 1000L +
                time.getNano() / 1_000_000L;
    }

    /**
     * 清理输出目录
     */
    private void cleanupOutputDirectory(Path outputDir) {
        if (outputDir == null || !Files.exists(outputDir)) {
            return;
        }

        try {
            Files.walk(outputDir)
                    .sorted(java.util.Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(file -> {
                        try {
                            Files.delete(file.toPath());
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", file.getAbsolutePath(), e);
                        }
                    });
            log.debug("清理输出目录完成: {}", outputDir.toAbsolutePath());
        } catch (IOException e) {
            log.error("清理输出目录失败: {}", outputDir.toAbsolutePath(), e);
        }
    }
}
