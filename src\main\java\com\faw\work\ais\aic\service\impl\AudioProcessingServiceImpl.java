package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.common.util.FileDownloadService;
import com.faw.work.ais.aic.service.AudioProcessingService;
import com.faw.work.ais.controller.dto.TimeStampDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 音频处理服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AudioProcessingServiceImpl implements AudioProcessingService {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final int MAX_CONCURRENT_TASKS = 3;

    @Autowired
    private FileDownloadService fileDownloadService;

    private final ExecutorService audioProcessingExecutor =
            Executors.newFixedThreadPool(MAX_CONCURRENT_TASKS, r -> {
                Thread t = new Thread(r, "audio-processing-" + System.currentTimeMillis());
                t.setDaemon(true);
                return t;
            });

    @Override
    public byte[] splitAndMergeAudio(String audioUrl, String timestamps) throws IOException {
        if (StrUtil.isBlank(audioUrl) || StrUtil.isBlank(timestamps)) {
            throw new IllegalArgumentException("音频URL和时间戳不能为空");
        }

        File downloadedFile = null;
        List<File> tempFiles = new ArrayList<>();

        try {
            // 1. 下载音频文件
            downloadedFile = fileDownloadService.downloadAudioFile(audioUrl);
            tempFiles.add(downloadedFile);

            // 2. 解析时间戳
            List<TimeStampDTO> timeStampList = parseTimestamps(timestamps);
            log.info("解析到 {} 个时间戳片段", timeStampList.size());

            // 3. 暂时返回原始音频文件的字节数组
            // TODO: 实现真正的音频切分和合并功能，需要集成JAVE库或FFmpeg
            log.warn("当前为简化实现，返回原始音频文件。实际项目中需要根据时间戳切分并合并音频片段。");
            return Files.readAllBytes(downloadedFile.toPath());

        } catch (Exception e) {
            log.error("音频处理失败", e);
            throw new IOException("音频处理失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            cleanupTempFiles(tempFiles);
        }
    }

    @Override
    public List<TimeStampDTO> parseTimestamps(String timestamps) {
        List<TimeStampDTO> result = new ArrayList<>();
        if (StrUtil.isBlank(timestamps)) {
            return result;
        }

        String[] timeRanges = timestamps.split(",");
        for (String timeRange : timeRanges) {
            String[] times = timeRange.trim().split("-");
            if (times.length == 2) {
                TimeStampDTO timeStamp = new TimeStampDTO();
                timeStamp.setStartTime(times[0].trim());
                timeStamp.setEndTime(times[1].trim());
                result.add(timeStamp);
            }
        }
        return result;
    }

    @Override
    public void cleanupTempFiles(List<File> files) {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (File file : files) {
            if (file != null && file.exists()) {
                try {
                    Files.delete(file.toPath());
                    log.debug("清理临时文件: {}", file.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
    }

    /**
     * 创建临时输出目录
     */
    private Path createTempOutputDirectory() throws IOException {
        Path outputDir = Files.createTempDirectory("audio_processing_");
        log.debug("创建临时输出目录: {}", outputDir.toAbsolutePath());
        return outputDir;
    }

    /**
     * 将时间转换为毫秒数
     */
    private long timeToMilliseconds(LocalTime time) {
        return (time.getHour() * 3600L + time.getMinute() * 60L + time.getSecond()) * 1000L +
                time.getNano() / 1_000_000L;
    }

    /**
     * 清理输出目录
     */
    private void cleanupOutputDirectory(Path outputDir) {
        if (outputDir == null || !Files.exists(outputDir)) {
            return;
        }

        try {
            Files.walk(outputDir)
                    .sorted(java.util.Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(file -> {
                        try {
                            Files.delete(file.toPath());
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", file.getAbsolutePath(), e);
                        }
                    });
            log.debug("清理输出目录完成: {}", outputDir.toAbsolutePath());
        } catch (IOException e) {
            log.error("清理输出目录失败: {}", outputDir.toAbsolutePath(), e);
        }
    }
}
