package com.faw.work.ais.aic.common.util;

import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileDownloadService {

    private static final String EXTENSION_M4A = ".m4a";
    private static final String EXTENSION_MP3 = ".mp3";
    private static final String EXTENSION_WAV = ".wav";
    private static final String CONTENT_TYPE_MP4 = "mp4";
    private static final String CONTENT_TYPE_M4A = "m4a";
    private static final String CONTENT_TYPE_MP3 = "mp3";
    private static final String CONTENT_TYPE_WAV = "wav";

    /**
     * 连接超时时间：5秒
     */
    private static final int CONNECT_TIMEOUT = 5000;

    /**
     * 读取超时时间：5分钟
     */
    private static final int READ_TIMEOUT = 30000;

    /**
     * 最大文件大小限制：100MB
     */
    private static final int MAX_FILE_SIZE = 50 * 1024 * 1024;
    /**
     * 从URL下载文件到临时目录
     *
     * @param audioUrl 音频文件URL
     * @return 下载的临时文件
     * @throws IOException 下载异常
     */
    public File downloadAudioFile(String audioUrl) throws IOException {
        if (audioUrl == null || audioUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("音频文件URL不能为空");
        }

        log.info("开始下载音频文件: {}", audioUrl);

        URL url = new URL(audioUrl);
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        File tempFile = null;

        try {
            // 建立连接
            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestMethod("GET");

            // 设置请求头
            connection.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "audio/*,*/*");

            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("下载失败，HTTP响应码: " + responseCode);
            }

            // 检查文件大小
            long contentLength = connection.getContentLengthLong();
            if (contentLength > MAX_FILE_SIZE) {
                throw new IOException("文件过大，超过限制: " + MAX_FILE_SIZE + " bytes");
            }

            // 检查Content-Type
            String contentType = connection.getContentType();
            if (contentType != null && !isAudioFile(contentType)) {
                log.warn("警告：文件类型可能不是音频文件: {}", contentType);
            }

            // 获取文件扩展名
            String fileExtension = getFileExtension(audioUrl, contentType);

            // 创建临时文件
            tempFile = File.createTempFile("downloaded_audio_", fileExtension);

            // 下载文件，写入临时文件
            inputStream = connection.getInputStream();
            long bytesDownloaded = Files.copy(inputStream, tempFile.toPath(),
                    StandardCopyOption.REPLACE_EXISTING);

            log.info("音频文件下载完成: {} -> {}, 大小: {} bytes",
                    audioUrl, tempFile.getAbsolutePath(), bytesDownloaded);

            return tempFile;

        } catch (IOException e) {
            // 清理失败的临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                } catch (IOException cleanupException) {
                    log.warn("清理失败的下载文件异常: {}", tempFile.getAbsolutePath(), cleanupException);
                }
            }
            throw new IOException("下载音频文件失败: " + e.getMessage(), e);
        } finally {
            // 关闭连接
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败", e);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 检查是否为音频文件
     */
    private boolean isAudioFile(String contentType) {
        return contentType != null && (
                contentType.startsWith("audio/") ||
                        contentType.contains("mp3") ||
                        contentType.contains("mp4") ||
                        contentType.contains("m4a") ||
                        contentType.contains("wav") ||
                        contentType.contains("flac")
        );
    }




    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String url, String contentType) {
        // 从Content-Type推断扩展名
        if (contentType != null) {
            if (contentType.contains(CONTENT_TYPE_MP4) || contentType.contains(CONTENT_TYPE_M4A)) {
                return EXTENSION_M4A;
            } else if (contentType.contains(CONTENT_TYPE_MP3)) {
                return EXTENSION_MP3;
            } else if (contentType.contains(CONTENT_TYPE_WAV)) {
                return EXTENSION_WAV;
            }
        }
        throw new BizException("无法从URL或Content-Type推断文件扩展名: " + url + ", " + contentType);
    }
}