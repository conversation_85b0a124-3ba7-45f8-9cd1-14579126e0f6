<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.MessageQueueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.faw.work.ais.aic.model.domain.MessageQueue">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="queue_name" property="queueName" />
        <result column="message_content" property="messageContent" />
        <result column="status" property="status" />
        <result column="retry_count" property="retryCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, queue_name, message_content, status, retry_count, create_time, update_time, remark
    </sql>
    <select id="selectByMessageId" resultType="com.faw.work.ais.aic.model.domain.MessageQueue">
        SELECT * FROM message_queue WHERE message_id = #{messageId}
    </select>
    <select id="selectByMessageContent" resultType="com.faw.work.ais.aic.model.domain.MessageQueue">
        SELECT * FROM message_queue WHERE message_content = #{messageContent}
    </select>

</mapper> 