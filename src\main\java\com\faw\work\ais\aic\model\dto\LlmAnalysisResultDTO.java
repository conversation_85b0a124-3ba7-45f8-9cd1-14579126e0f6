package com.faw.work.ais.aic.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "LLM分析结果DTO")
public class LlmAnalysisResultDTO {

    @Data
    @Schema(description = "客户分析结果")
    public static class CustomerAnalysis {

        @Schema(description = "客户问题总结")
        private List<TagAnalysisDTO.TopicSummary> customerQuestionSummaries;

        @Schema(description = "客户情绪")
        private String emotion;

        @Schema(description = "原因")
        private String reason;

        @Schema(description = "得分")
        private String score;

        @Schema(description = "开始时间")
        private String start;

        @Schema(description = "结束时间")
        private String end;
    }

    @Data
    @Schema(description = "需求标签分析结果")
    public static class DemandTagsAnalysis {

        @Schema(description = "需求标签")
        private List<TagAnalysisDTO.DemandTag> demandTags;

        @Schema(description = "AI生成的需求标签")
        private List<TagAnalysisDTO.DemandTag> aiDemandTags;
    }

}
