package com.faw.work.ais.aic.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.faw.work.ais.aic.model.dto.OmniRequest;
import com.faw.work.ais.aic.service.OmniService;
import com.faw.work.ais.common.enums.Constants;
import com.faw.work.ais.common.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OmniServiceImpl implements OmniService {

    @Value("${ali.omini.api-key:}")
    private String apiKey;

    @Value("${ali.omini.url:https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions}")
    private String ominiUrl;

    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.custom()
            .setDefaultRequestConfig(RequestConfig.custom()
                    .setConnectionRequestTimeout(Timeout.ofSeconds(10))
                    .setResponseTimeout(Timeout.ofSeconds(60))
                    .build())
            .build();

    @Override
    public String processAudioWithTextStream(String audioUrl, String text) {
        // 构建请求内容
        List<OmniRequest.OmniContent> contentList = new ArrayList<>();

        // 添加音频内容
        Map<String, Object> inputAudio = new HashMap<>(16);
        inputAudio.put("data", audioUrl);
        inputAudio.put("format", "wav");

        contentList.add(OmniRequest.OmniContent.builder()
                .type("input_audio")
                .input_audio(inputAudio)
                .build());

        // 添加文本内容
        contentList.add(OmniRequest.OmniContent.builder()
                .type("text")
                .text(text)
                .build());

        // 构建消息
        List<OmniRequest.OmniMessage> messages = Collections.singletonList(
                OmniRequest.OmniMessage.builder()
                        .role("user")
                        .content(contentList)
                        .build()
        );

        // 构建流式请求实体
        Map<String, Object> streamOptions = new HashMap<>(16);
        streamOptions.put("include_usage", true);

        OmniRequest requestEntity = OmniRequest.builder()
                .model("qwen-omni-turbo")
                .messages(messages)
                .stream(true)
                .stream_options(streamOptions)
                .modalities(Arrays.asList("text", "audio"))
                .audio(Map.of("voice", "Cherry", "format", "wav"))
                .build();

        return executeOmniStreamRequest(requestEntity);
    }

    @Override
    public String processAudioWithAudioBase64(byte[] audioBytes, String prompt, String systemMessage) throws IOException {
        String base64Audio = Base64.getEncoder().encodeToString(audioBytes);
        String audioDataUri = "data:;base64," + base64Audio;

        List<OmniRequest.OmniMessage> messages = new ArrayList<>();

        // 如果提供了系统角色信息，则按照官方文档建议的方式添加角色设定对话
        if (systemMessage != null && !systemMessage.trim().isEmpty()) {
            // 1. 添加角色设定的 User Message
            messages.add(OmniRequest.OmniMessage.builder()
                    .role("user")
                    .content(Collections.singletonList(
                            OmniRequest.OmniContent.builder().type("text").text(systemMessage).build()
                    ))
                    .build());

            // 2. 添加角色设定的 Assistant Message
            messages.add(OmniRequest.OmniMessage.builder()
                    .role("assistant")
                    .content(Collections.singletonList(
                            OmniRequest.OmniContent.builder().type("text").text("好的，我记住了你的设定。").build()
                    ))
                    .build());
        }

        // 3. 添加包含音频和实际问题的 User Message
        List<OmniRequest.OmniContent> userQueryContent = new ArrayList<>();

        Map<String, Object> inputAudio = new HashMap<>(16);
        inputAudio.put("data", audioDataUri);
        inputAudio.put("format", "wav");

        userQueryContent.add(OmniRequest.OmniContent.builder()
                .type("input_audio")
                .input_audio(inputAudio)
                .build());

        userQueryContent.add(OmniRequest.OmniContent.builder()
                .type("text")
                .text(prompt)
                .build());

        messages.add(OmniRequest.OmniMessage.builder()
                .role("user")
                .content(userQueryContent)
                .build());


        Map<String, Object> streamOptions = new HashMap<>(16);
        streamOptions.put("include_usage", true);

        OmniRequest requestEntity = OmniRequest.builder()
                .model("qwen-omni-turbo")
                .messages(messages)
                .stream(true)
                .stream_options(streamOptions)
                .modalities(Arrays.asList("text", "audio"))
                .audio(Map.of("voice", "Cherry", "format", "wav"))
                .build();

        return executeOmniStreamRequest(requestEntity);
    }

    private String executeOmniStreamRequest(OmniRequest requestEntity) {
        String requestId = UUID.randomUUID().toString();
        String requestJson = JSON.toJSONString(requestEntity);

        log.debug("阿里云Omni模型流式请求: {}", requestJson);

        HttpPost httpPost = new HttpPost(ominiUrl);
        httpPost.setHeader("Authorization", "Bearer " + apiKey);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("X-DashScope-RequestId", requestId);
        httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

        try {
            return HTTP_CLIENT.execute(httpPost, response -> {
                int statusCode = response.getCode();

                if (statusCode != Constants.SUCCESS) {
                    String errorBody = response.getEntity() != null ?
                            EntityUtils.toString(response.getEntity()) : "未知错误";
                    log.error("阿里云Omni模型流式调用失败，状态码: {}, 错误信息: {}", statusCode, errorBody);
                    throw new BizException("调用阿里云Omni模型失败: " + errorBody);
                }

                if (response.getEntity() == null) {
                    throw new BizException("响应体为空");
                }

                // 修改点：直接拼接transcript文本
                StringBuilder transcriptBuilder = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(response.getEntity().getContent()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (line.startsWith("data: ")) {
                            String data = line.substring(6);
                            if ("[DONE]".equals(data)) {
                                break;
                            }

                            try {
                                JSONObject chunk = JSON.parseObject(data);
                                JSONArray choices = chunk.getJSONArray("choices");
                                if (choices != null && !choices.isEmpty()) {
                                    JSONObject audio = choices.getJSONObject(0)
                                            .getJSONObject("delta")
                                            .getJSONObject("audio");

                                    if (audio != null) {
                                        String transcript = audio.getString("transcript");
                                        if (transcript != null && !transcript.isEmpty()) {
                                            transcriptBuilder.append(transcript);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("解析响应块失败: {}", data, e);
                            }
                        }
                    }
                }
                return transcriptBuilder.toString();
            });
        } catch (IOException e) {
            log.error("调用阿里云Omni模型流式请求发生IO异常", e);
            throw new BizException("网络请求失败: " + e.getMessage());
        }
    }
}
