package com.faw.work.ais.aic.service;

import java.io.IOException;

public interface OmniService {

    /**
     * 流式调用阿里云Omni模型
     * @param audioUrl 音频文件URL
     * @param text 文本内容
     * @return 响应流
     */
    String processAudioWithTextStream(String audioUrl, String text);

    /**
     * 流式调用阿里云Omni模型
     *
     * @param audioBytes    音频字节
     * @param prompt        提示词
     * @param systemMessage 系统消息
     * @return 响应流
     * @throws IOException IOException
     */
    String processAudioWithAudioBase64(byte[] audioBytes, String prompt, String systemMessage) throws IOException;
}
