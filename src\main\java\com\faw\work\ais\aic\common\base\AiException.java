package com.faw.work.ais.aic.common.base;


import com.faw.work.ais.common.enums.IEnum;
import com.faw.work.ais.common.enums.ResEnum;
import com.faw.work.ais.common.exception.CustomErrorWrapper;
import lombok.Data;
import lombok.Getter;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Getter
public class AiException extends RuntimeException {

	@Serial
    private static final long serialVersionUID = 5188502951761537232L;

    private final Integer code;
    /**
     * Constructs with a code and message. Message is used as the exception detail.
     */
    public AiException(Integer code, String msg) {
        super(msg);
        this.code = code;
    }

    /**
     * Constructs with a message. Message is used as the exception detail.
     */
    public AiException(String msg) {
        super(msg);
        this.code = 500;
    }

    public AiException(String msg,Throwable throwable) {
        super(msg,throwable);
        this.code = 500;
    }
}
